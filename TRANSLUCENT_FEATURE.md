# 窗口模糊功能 (Translucent Feature)

## 功能说明

这个功能允许您将整个Electron浏览器窗口设置为半透明模糊状态，可以看到窗口后面的内容但是带有模糊效果，类似于毛玻璃效果。

## 如何使用

### 方法1: 菜单快捷键
- 按 `Cmd+Shift+;` (macOS) 或 `Ctrl+Shift+;` (Windows/Linux) 打开模糊度设置对话框
- 输入 0.0 到 1.0 之间的数值：
  - `0.0` = 关闭模糊效果，窗口恢复正常
  - `0.1-0.2` = 轻微模糊效果
  - `0.3-0.5` = 中等模糊效果
  - `0.6-0.8` = 较强模糊效果
  - `0.9-1.0` = 最强模糊效果

### 方法2: 应用菜单
1. 点击菜单栏中的 "Navigation" 菜单
2. 选择 "Set Translucent" 选项
3. 在弹出的对话框中输入模糊度数值

## 测试步骤

1. 启动应用程序：`npm start`
2. 按 `Cmd+Shift+;` (macOS) 打开模糊度设置
3. 尝试不同的数值：
   - 输入 `0.5` 然后按回车 - 应该看到中等模糊效果
   - 输入 `1.0` 然后按回车 - 应该看到最强模糊效果
   - 输入 `0.0` 然后按回车 - 应该关闭模糊效果

## 技术实现

### macOS系统
- 使用Electron的 `transparent: true` 属性启用窗口透明
- 使用 `setVibrancy()` API设置不同的模糊效果类型
- 根据模糊度值选择不同的vibrancy类型：
  - 0.0-0.2: 'under-window'
  - 0.2-0.4: 'content'
  - 0.4-0.6: 'window'
  - 0.6-0.8: 'popover'
  - 0.8-1.0: 'menu'
- 同时调整窗口透明度来控制模糊程度

### Windows/Linux系统
- 使用透明度模拟模糊效果
- 通过调整窗口的 `opacity` 属性实现

## 设置保存

模糊度设置会自动保存到 `bookmarks.json` 文件中的 `settings.translucent` 字段，下次启动程序时会自动应用保存的设置。

## 注意事项

1. 此功能在macOS上效果最佳，因为系统原生支持vibrancy效果
2. 在Windows和Linux上只能通过透明度模拟模糊效果
3. 模糊效果会影响窗口内容的可读性，建议根据使用场景调整合适的数值
4. 设置为0.0可以完全关闭模糊效果，恢复正常窗口显示

## 与透明度功能的区别

- **透明度功能** (`Cmd+;`): 只是简单地调整窗口的不透明度
- **模糊功能** (`Cmd+Shift+;`): 在透明的基础上添加模糊效果，创造毛玻璃视觉效果
