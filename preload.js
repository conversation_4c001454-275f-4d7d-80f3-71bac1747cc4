const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// Listen for messages from homepage
window.addEventListener('message', (event) => {
  if (event.data.type === 'loadURL') {
    ipcRenderer.sendToHost('loadURL', event.data.url);
  } else if (event.data.type === 'getBookmarks') {
    // Forward the get-bookmarks request to the main process
    ipcRenderer.invoke('get-bookmarks').then(result => {
      // Send the result back to the webpage
      window.postMessage({ type: 'bookmarksResult', data: result }, '*');
    });
  } else if (event.data.type === 'deleteBookmark') {
    // Forward the delete-bookmark request to the main process
    ipcRenderer.invoke('delete-bookmark', event.data.url).then(result => {
      // If deletion was successful, reload bookmarks
      if (result.success) {
        // Trigger a reload of bookmarks
        window.postMessage({ type: 'getBookmarks' }, '*');
      }
    });
  } else if (event.data.type === 'create-new-window') {
    // Forward the create-new-window request to the host
    ipcRenderer.sendToHost('create-new-window', event.data.url);
  }
});

// Expose ipcRenderer.invoke for bookmarks operations
window.ipcRenderer = {
  invoke: async (channel, ...args) => {
    if (channel === 'get-bookmarks') {
      return ipcRenderer.invoke('get-bookmarks');
    }
    return null;
  }
};

// Add throttling related variables
let lastMouseNavigationTime = 0;
const MOUSE_NAVIGATION_COOLDOWN = 500; // 500ms cooldown time

document.addEventListener('mouseup', (e) => {
  console.log('Mouse button clicked:', e.button);
  const now = Date.now();
  
  // Check if in cooldown period
  if (now - lastMouseNavigationTime < MOUSE_NAVIGATION_COOLDOWN) {
    return;
  }
  
  if (e.button === 3 || e.button === 8) {
    lastMouseNavigationTime = now;
    ipcRenderer.sendToHost('go-back');
  }
  else if (e.button === 4 || e.button === 9) {
    lastMouseNavigationTime = now;
    ipcRenderer.sendToHost('go-forward');
  }
}, false);

// Swipe navigation related variables
let lastNavigationTime = 0;
let lastWheelTime = 0;
let swipeTimeout = null;
let totalDeltaX = 0;

const NAVIGATION_COOLDOWN = 1000; // Navigation cooldown time (ms)
const SWIPE_THRESHOLD = 50;      // Swipe threshold
const SWIPE_TIMEOUT = 100;       // Swipe end detection time (ms)

// Trigger navigation
function triggerNavigation() {
  const now = Date.now();
  
  // Check if in cooldown period
  if (now - lastNavigationTime < NAVIGATION_COOLDOWN) {
    return;
  }

  // Check if threshold is reached
  if (Math.abs(totalDeltaX) >= SWIPE_THRESHOLD) {
    lastNavigationTime = now;
    const direction = Math.sign(totalDeltaX);

    if (direction > 0) {
      console.log('Going forward...');
      ipcRenderer.sendToHost('go-forward');
    } else {
      console.log('Going back...');
      ipcRenderer.sendToHost('go-back');
    }
  }

  // Reset state
  totalDeltaX = 0;
}

// Handle swipe events
function handleWheel(e) {
  const now = Date.now();

  // Only handle horizontal swipes (two-finger left/right swipe)
  if (Math.abs(e.deltaX) > Math.abs(e.deltaY)) {
    e.preventDefault();

    // Clear previous timeout
    if (swipeTimeout) {
      clearTimeout(swipeTimeout);
    }

    // If it's a new swipe sequence
    if (now - lastWheelTime > NAVIGATION_COOLDOWN) {
      totalDeltaX = 0;
    }

    // Accumulate swipe distance
    totalDeltaX += e.deltaX;
    lastWheelTime = now;

    // Set new timeout to trigger navigation when swipe ends
    swipeTimeout = setTimeout(() => {
      triggerNavigation();
      swipeTimeout = null;
    }, SWIPE_TIMEOUT);
  }
}

// Add zoom related variables
let lastScale = 1.0;
const MIN_SCALE = 0.5;
const MAX_SCALE = 3.0;

// Handle all wheel events
document.addEventListener('wheel', (e) => {
    // If Command/Ctrl key is pressed
    if (e.metaKey || e.ctrlKey) {
        e.preventDefault();
        e.stopPropagation();
        
        // Calculate zoom increment
        const delta = -e.deltaY * 0.01; // Convert to more suitable zoom increment
        ipcRenderer.sendToHost('zoom-wheel', delta);
        
        console.log('Wheel zoom:', delta); // Debug log
    }
    // Otherwise continue processing existing horizontal swipe logic
    else if (Math.abs(e.deltaX) > Math.abs(e.deltaY)) {
        handleWheel(e);
    }
}, { passive: false });

// Handle pinch/spread gestures
let initialGestureDistance = null;

document.addEventListener('touchstart', (e) => {
    if (e.touches.length === 2) {
        e.preventDefault();
        const touch1 = e.touches[0];
        const touch2 = e.touches[1];
        initialGestureDistance = Math.hypot(
            touch2.clientX - touch1.clientX,
            touch2.clientY - touch1.clientY
        );
        console.log('Touch start:', initialGestureDistance); // Debug log
    }
}, { passive: false });

document.addEventListener('touchmove', (e) => {
    if (e.touches.length === 2 && initialGestureDistance !== null) {
        e.preventDefault();
        const touch1 = e.touches[0];
        const touch2 = e.touches[1];
        const currentDistance = Math.hypot(
            touch2.clientX - touch1.clientX,
            touch2.clientY - touch1.clientY
        );
        
        const scale = currentDistance / initialGestureDistance;
        ipcRenderer.sendToHost('zoom-gesture', scale);
        
        console.log('Touch move:', scale); // Debug log
    }
}, { passive: false });

document.addEventListener('touchend', (e) => {
    if (e.touches.length < 2) {
        initialGestureDistance = null;
        console.log('Touch end'); // Debug log
    }
}, { passive: false });

// Modify userScripts API - directly expose to window
window.userScripts = {
  loadUserScripts: () => ipcRenderer.invoke('load-user-scripts')
};

// Add GM_ functions that might be needed by userscripts
window.GM_info = {
  script: {
    name: 'userscript',
    namespace: 'electron-userscript'
  },
  version: '1.0'
};

window.GM_addStyle = (css) => {
  const style = document.createElement('style');
  style.textContent = css;
  document.head.appendChild(style);
  return style;
};

window.GM_xmlhttpRequest = function(details) {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    
    xhr.open(details.method || 'GET', details.url, true);
    
    // Set timeout if specified
    if (details.timeout) {
      xhr.timeout = details.timeout;
    }
    
    // Set responseType if specified
    if (details.responseType) {
      xhr.responseType = details.responseType;
    }
    
    // Set all the requested headers
    if (details.headers) {
      Object.entries(details.headers).forEach(([key, value]) => {
        xhr.setRequestHeader(key, value);
      });
    }

    // Set overrideMimeType if specified
    if (details.overrideMimeType) {
      xhr.overrideMimeType(details.overrideMimeType);
    }

    // Handle successful response
    xhr.onload = function() {
      const response = {
        status: xhr.status,
        statusText: xhr.statusText,
        responseHeaders: xhr.getAllResponseHeaders(),
        readyState: xhr.readyState,
        finalUrl: xhr.responseURL,
        response: xhr.response,
        responseText: xhr.responseText
      };
      
      if (details.onload) {
        details.onload(response);
      }
      resolve(response);
    };

    // Handle network errors
    xhr.onerror = function(error) {
      const response = {
        error: error,
        status: xhr.status,
        statusText: xhr.statusText,
        readyState: xhr.readyState
      };
      
      if (details.onerror) {
        details.onerror(response);
      }
      reject(response);
    };

    // Handle timeouts
    xhr.ontimeout = function() {
      const response = {
        error: 'timeout',
        status: xhr.status,
        statusText: xhr.statusText,
        readyState: xhr.readyState
      };
      
      if (details.ontimeout) {
        details.ontimeout(response);
      }
      reject(response);
    };

    // Handle progress if requested
    if (details.onprogress) {
      xhr.onprogress = details.onprogress;
    }

    // Handle upload progress if requested
    if (details.upload && details.upload.onprogress) {
      xhr.upload.onprogress = details.upload.onprogress;
    }

    // Handle abort if requested
    if (details.onabort) {
      xhr.onabort = details.onabort;
    }

    // Send the request with optional data
    try {
      xhr.send(details.data);
    } catch (error) {
      reject({
        error: error,
        status: 0,
        statusText: 'Request failed to send'
      });
    }

    // Return the XHR object for potential abort() calls
    return xhr;
  });
};

window.GM_setValue = (key, value) => {
  localStorage.setItem(`GM_${key}`, JSON.stringify(value));
};

window.GM_getValue = (key, defaultValue) => {
  const value = localStorage.getItem(`GM_${key}`);
  return value === null ? defaultValue : JSON.parse(value);
};

// Add missing GM_ functions
window.GM_deleteValue = (key) => {
  localStorage.removeItem(`GM_${key}`);
};

window.GM_listValues = () => {
  const values = [];
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key.startsWith('GM_')) {
      values.push(key.slice(3)); // Remove 'GM_' prefix
    }
  }
  return values;
};

window.GM_getResourceText = (name) => {
  return '';  // Implement if needed
};

window.GM_getResourceURL = (name) => {
  return '';  // Implement if needed
};

window.GM_registerMenuCommand = (name, fn) => {
  // Could implement this with a custom UI if needed
  console.log('Menu command registered:', name);
};

window.GM_unregisterMenuCommand = (name) => {
  // Could implement this with a custom UI if needed
  console.log('Menu command unregistered:', name);
};

window.GM_openInTab = (url, options) => {
  window.open(url, '_blank');
};

window.GM_download = (details) => {
  const a = document.createElement('a');
  a.href = details.url;
  a.download = details.name || '';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
};

window.GM_log = console.log.bind(console);

window.GM_notification = (details) => {
  if (Notification.permission === 'granted') {
    new Notification(details.title || 'Notification', {
      body: details.text,
      icon: details.image
    });
  }
};

// Add unsafeWindow
window.unsafeWindow = window;

// 添加事件处理函数来防止重复点击
document.addEventListener('click', (e) => {
  // Handle Shift + Click for any element to force new window
  if (e.shiftKey || isShiftKeyPressed) {
    // Try to find a URL from the clicked element or its parents
    let targetElement = e.target;
    let targetUrl = null;
    
    // Check up to 5 parent levels to find a URL
    for (let i = 0; i < 5 && targetElement && !targetUrl; i++) {
      targetUrl = targetElement.href || 
                 targetElement.getAttribute('data-href') || 
                 targetElement.getAttribute('formaction') || 
                 targetElement.getAttribute('data-url') || 
                 targetElement.getAttribute('data-link');
      
      if (!targetUrl) {
        targetElement = targetElement.parentElement;
      }
    }

    // If we found a URL and it's http/https, create a new window
    if (targetUrl && (targetUrl.startsWith('http://') || targetUrl.startsWith('https://'))) {
      e.preventDefault();
      e.stopPropagation();
      console.log('Shift+Click: Opening in new window:', targetUrl);
      ipcRenderer.sendToHost('create-new-window', targetUrl);
      return;
    }
  }
  
  // Continue with original link click prevention logic
  if (e.target.tagName === 'A') {
    // 检查是否已经处理过这个点击事件
    if (e.target.dataset.processed) {
      e.preventDefault();
      //e.stopPropagation();  // 阻止事件冒泡
      return;
    }
    
    // 标记这个链接已被处理
    e.target.dataset.processed = true;
    
    // 100ms后清除标记，允许下次点击
    setTimeout(() => {
      delete e.target.dataset.processed;
    }, 250);
  }
});

// Add early link and button click handler that works before page fully loads
function installEarlyClickHandler() {
  // Create a MutationObserver to watch for DOM changes
  const observer = new MutationObserver((mutations) => {
    // Process all added nodes to apply click handlers
    mutations.forEach(mutation => {
      mutation.addedNodes.forEach(node => {
        // Check if the node is an element
        if (node.nodeType === 1) {
          // Add handler to all elements to check for shift+click
          if (node.nodeType === 1) {
            attachClickHandler(node);
          }
          
          // Also check all child nodes that are elements
          if (node.querySelectorAll) {
            const elements = node.querySelectorAll('*');
            elements.forEach(el => attachClickHandler(el));
          }
        }
      });
    });
  });
  
  // Function to attach click handler to an element
  function attachClickHandler(element) {
    if (element.__handlerAttached) return; // Prevent double handlers
    
    element.addEventListener('click', (e) => {
      // First check for Shift key - this overrides all other behavior
      if (e.shiftKey || isShiftKeyPressed) {
        // Try to find a URL from the clicked element or its parents
        let targetElement = e.target;
        let targetUrl = null;
        
        // Check up to 5 parent levels to find a URL
        for (let i = 0; i < 5 && targetElement && !targetUrl; i++) {
          targetUrl = targetElement.href || 
                     targetElement.getAttribute('data-href') || 
                     targetElement.getAttribute('formaction') || 
                     targetElement.getAttribute('data-url') || 
                     targetElement.getAttribute('data-link');
          
          if (!targetUrl) {
            targetElement = targetElement.parentElement;
          }
        }

        // If we found a URL and it's http/https, create a new window
        if (targetUrl && (targetUrl.startsWith('http://') || targetUrl.startsWith('https://'))) {
          e.preventDefault();
          e.stopPropagation();
          console.log('Shift+Click (early handler): Opening in new window:', targetUrl);
          ipcRenderer.sendToHost('create-new-window', targetUrl);
          return;
        }
      }
      
      // If not shift+click, continue with original logic for links and buttons
      
      // Get target URL
      let targetUrl = element.href || element.getAttribute('data-href');
      
      // For buttons, check additional attributes
      if (element.tagName === 'BUTTON') {
        targetUrl = targetUrl || 
                   element.getAttribute('formaction') || 
                   element.getAttribute('data-url') ||
                   element.getAttribute('data-link');
      }
      
      // Check for target="_blank"
      let hasTargetBlank = element.getAttribute('target') === '_blank' || 
                           element.getAttribute('rel')?.includes('external');
      
      // Skip handling if:
      // 1. Element has onclick attribute
      // 2. URL is javascript: protocol
      // 3. Has target="_blank" but URL is not http/https
      if (element.hasAttribute('onclick') || 
          targetUrl?.startsWith('javascript:') ||
          (hasTargetBlank && !(targetUrl && (targetUrl.startsWith('http://') || targetUrl.startsWith('https://'))))) {
        return;
      }
      
      // Handle HTTP/HTTPS URLs
      if (targetUrl && (targetUrl.startsWith('http://') || targetUrl.startsWith('https://'))) {
        // If link should open in new window
        if (hasTargetBlank) {
          e.preventDefault();
          // Send message to create new window
          ipcRenderer.sendToHost('create-new-window', targetUrl);
          return;
        }
        
        // Otherwise let the default behavior happen
      }
    }, true);
    
    // Mark as handled
    element.__handlerAttached = true;
  }
  
  // Start observing as soon as body is available
  function startObserving() {
    if (document.body) {
      // First handle existing elements - attach to ALL elements
      const elements = document.querySelectorAll('*');
      elements.forEach(el => attachClickHandler(el));
      
      // Then observe future changes
      observer.observe(document.body, { 
        childList: true, 
        subtree: true 
      });
      
      // Also override window.open
      overrideWindowOpen();
    } else {
      // If body isn't available yet, try again soon
      setTimeout(startObserving, 10);
    }
  }
  
  // Override window.open
  function overrideWindowOpen() {
    const originalWindowOpen = window.open;
    window.open = function(url, target, features) {
      // Check for Shift key pressed during window.open call
      if ((window.event && window.event.shiftKey || isShiftKeyPressed) && url && (url.startsWith('http://') || url.startsWith('https://'))) {
        ipcRenderer.sendToHost('create-new-window', url);
        return null;
      }
      
      // Let original window.open handle these cases:
      // 1. Has window features
      // 2. Not an http/https URL
      if ((features && features.trim() !== '') || !url || !(url.startsWith('http://') || url.startsWith('https://'))) {
        return originalWindowOpen(url, target, features);
      }
      
      // Handle target="_blank" case
      if (target === '_blank') {
        ipcRenderer.sendToHost('create-new-window', url);
        return null;
      }
      
      // Let default behavior handle the rest
      return originalWindowOpen(url, target, features);
    };
  }
  
  // Start the process
  startObserving();
}

// Track Shift key state globally to handle programmatic navigation while Shift is pressed
let isShiftKeyPressed = false;

document.addEventListener('keydown', (e) => {
  if (e.key === 'Shift') {
    isShiftKeyPressed = true;
  }
});

document.addEventListener('keyup', (e) => {
  if (e.key === 'Shift') {
    isShiftKeyPressed = false;
  }
});

// Make sure we reset the shift key state when window loses focus
window.addEventListener('blur', () => {
  isShiftKeyPressed = false;
});

// Call immediately when preload script runs
installEarlyClickHandler();

// Also make sure to run again when DOM content is loaded
document.addEventListener('DOMContentLoaded', installEarlyClickHandler); 